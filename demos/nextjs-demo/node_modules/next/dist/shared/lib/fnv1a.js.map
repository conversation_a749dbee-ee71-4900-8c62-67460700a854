{"version": 3, "sources": ["../../../src/shared/lib/fnv1a.ts"], "names": ["fnv1a", "FNV_PRIMES", "BigInt", "FNV_OFFSETS", "inputString", "size", "seed", "Error", "hash", "fnvPrime", "isUnicoded", "index", "length", "characterCode", "charCodeAt", "unescape", "encodeURIComponent", "asUintN"], "mappings": "AAAA,gDAAgD;AAChD,kCAAkC;AAClC,iEAAiE;AACjE,0DAA0D;;;;+BAgC1D;;;eAAwBA;;;AA9BxB,MAAMC,aAAa;IACjB,IAAIC,OAAO;IACX,IAAIA,OAAO;IACX,KAAKA,OAAO;IACZ,KAAKA,OACH;IAEF,KAAKA,OACH;IAEF,MAAMA,OACJ;AAEJ;AAEA,MAAMC,cAAc;IAClB,IAAID,OAAO;IACX,IAAIA,OAAO;IACX,KAAKA,OAAO;IACZ,KAAKA,OACH;IAEF,KAAKA,OACH;IAEF,MAAMA,OACJ;AAEJ;AAEe,SAASF,MACtBI,WAAmB,EACnB;IAAA,IAAA,EACEC,OAAO,EAAE,EACTC,OAAO,CAAC,EAIT,GAND,mBAMI,CAAC,IANL;IAQA,IAAI,CAACL,UAAU,CAACI,KAAK,EAAE;QACrB,MAAM,IAAIE,MACR;IAEJ;IAEA,IAAIC,OAAeL,WAAW,CAACE,KAAK,GAAGH,OAAOI;IAC9C,MAAMG,WAAWR,UAAU,CAACI,KAAK;IAEjC,oCAAoC;IACpC,IAAIK,aAAa;IAEjB,IAAK,IAAIC,QAAQ,GAAGA,QAAQP,YAAYQ,MAAM,EAAED,QAAS;QACvD,IAAIE,gBAAgBT,YAAYU,UAAU,CAACH;QAE3C,wDAAwD;QACxD,IAAIE,gBAAgB,QAAQ,CAACH,YAAY;YACvCN,cAAcW,SAASC,mBAAmBZ;YAC1CS,gBAAgBT,YAAYU,UAAU,CAACH;YACvCD,aAAa;QACf;QAEAF,QAAQN,OAAOW;QACfL,OAAON,OAAOe,OAAO,CAACZ,MAAMG,OAAOC;IACrC;IAEA,OAAOD;AACT"}