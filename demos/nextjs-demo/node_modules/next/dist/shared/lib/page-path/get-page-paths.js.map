{"version": 3, "sources": ["../../../../src/shared/lib/page-path/get-page-paths.ts"], "names": ["getPagePaths", "normalizedPagePath", "extensions", "isAppDir", "page", "denormalizePagePath", "prefixes", "endsWith", "path", "join", "paths", "extension", "prefix", "push"], "mappings": ";;;;+BAeg<PERSON>;;;eAAAA;;;;qCAfoB;+DACnB;AAcV,SAASA,aACdC,kBAA0B,EAC1BC,UAAoB,EACpBC,QAAiB;IAEjB,MAAMC,OAAOC,IAAAA,wCAAmB,EAACJ;IAEjC,IAAIK;IACJ,IAAIH,UAAU;QACZG,WAAW;YAACF;SAAK;IACnB,OAAO,IAAIH,mBAAmBM,QAAQ,CAAC,WAAW;QAChDD,WAAW;YAACE,aAAI,CAACC,IAAI,CAACL,MAAM;SAAS;IACvC,OAAO;QACLE,WAAW;YAACF;YAAMI,aAAI,CAACC,IAAI,CAACL,MAAM;SAAS;IAC7C;IAEA,MAAMM,QAAkB,EAAE;IAC1B,KAAK,MAAMC,aAAaT,WAAY;QAClC,KAAK,MAAMU,UAAUN,SAAU;YAC7BI,MAAMG,IAAI,CAAC,AAAGD,SAAO,MAAGD;QAC1B;IACF;IAEA,OAAOD;AACT"}