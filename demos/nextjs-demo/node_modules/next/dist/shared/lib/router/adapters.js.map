{"version": 3, "sources": ["../../../../src/shared/lib/router/adapters.tsx"], "names": ["adaptForAppRouterInstance", "adaptForSearchParams", "adaptForPathParams", "PathnameContextProviderAdapter", "router", "back", "forward", "refresh", "reload", "push", "href", "scroll", "undefined", "replace", "prefetch", "isReady", "query", "URLSearchParams", "asPathToSearchParams", "<PERSON><PERSON><PERSON>", "pathParams", "routeRegex", "getRouteRegex", "pathname", "keys", "Object", "groups", "key", "children", "props", "ref", "useRef", "isAutoExport", "value", "useMemo", "current", "isDynamicRoute", "<PERSON><PERSON><PERSON><PERSON>", "url", "URL", "_", "PathnameContext", "Provider"], "mappings": ";;;;;;;;;;;;;;;;;IAmBgBA,yBAAyB;eAAzBA;;IA+BAC,oBAAoB;eAApBA;;IAUAC,kBAAkB;eAAlBA;;IAeAC,8BAA8B;eAA9BA;;;;iEApEuB;iDACP;uBACD;sCACM;4BACP;AAQvB,SAASH,0BACdI,MAAkB;IAElB,OAAO;QACLC;YACED,OAAOC,IAAI;QACb;QACAC;YACEF,OAAOE,OAAO;QAChB;QACAC;YACEH,OAAOI,MAAM;QACf;QACAC,MAAKC,IAAY,EAAE;YAAA,IAAA,EAAEC,MAAM,EAAmB,GAA3B,mBAA8B,CAAC,IAA/B;YACjB,KAAKP,OAAOK,IAAI,CAACC,MAAME,WAAW;gBAAED;YAAO;QAC7C;QACAE,SAAQH,IAAY,EAAE;YAAA,IAAA,EAAEC,MAAM,EAAmB,GAA3B,mBAA8B,CAAC,IAA/B;YACpB,KAAKP,OAAOS,OAAO,CAACH,MAAME,WAAW;gBAAED;YAAO;QAChD;QACAG,UAASJ,IAAY;YACnB,KAAKN,OAAOU,QAAQ,CAACJ;QACvB;IACF;AACF;AAQO,SAAST,qBACdG,MAAwD;IAExD,IAAI,CAACA,OAAOW,OAAO,IAAI,CAACX,OAAOY,KAAK,EAAE;QACpC,OAAO,IAAIC;IACb;IAEA,OAAOC,IAAAA,0CAAoB,EAACd,OAAOe,MAAM;AAC3C;AAEO,SAASjB,mBACdE,MAAqE;IAErE,IAAI,CAACA,OAAOW,OAAO,IAAI,CAACX,OAAOY,KAAK,EAAE;QACpC,OAAO;IACT;IACA,MAAMI,aAAqB,CAAC;IAC5B,MAAMC,aAAaC,IAAAA,yBAAa,EAAClB,OAAOmB,QAAQ;IAChD,MAAMC,OAAOC,OAAOD,IAAI,CAACH,WAAWK,MAAM;IAC1C,KAAK,MAAMC,OAAOH,KAAM;QACtBJ,UAAU,CAACO,IAAI,GAAGvB,OAAOY,KAAK,CAACW,IAAI;IACrC;IACA,OAAOP;AACT;AAEO,SAASjB,+BAA+B,KAO7C;IAP6C,IAAA,EAC7CyB,QAAQ,EACRxB,MAAM,EACN,GAAGyB,OAIH,GAP6C;IAQ7C,MAAMC,MAAMC,IAAAA,aAAM,EAACF,MAAMG,YAAY;IACrC,MAAMC,QAAQC,IAAAA,cAAO,EAAC;QACpB,wEAAwE;QACxE,2EAA2E;QAC3E,iDAAiD;QACjD,MAAMF,eAAeF,IAAIK,OAAO;QAChC,IAAIH,cAAc;YAChBF,IAAIK,OAAO,GAAG;QAChB;QAEA,sEAAsE;QACtE,qDAAqD;QACrD,IAAIC,IAAAA,qBAAc,EAAChC,OAAOmB,QAAQ,GAAG;YACnC,yEAAyE;YACzE,uEAAuE;YACvE,MAAM;YACN,sFAAsF;YACtF,IAAInB,OAAOiC,UAAU,EAAE;gBACrB,OAAO;YACT;YAEA,oEAAoE;YACpE,wEAAwE;YACxE,mEAAmE;YACnE,mBAAmB;YACnB,0EAA0E;YAC1E,IAAIL,gBAAgB,CAAC5B,OAAOW,OAAO,EAAE;gBACnC,OAAO;YACT;QACF;QAEA,2EAA2E;QAC3E,2EAA2E;QAC3E,2BAA2B;QAC3B,kEAAkE;QAClE,IAAIuB;QACJ,IAAI;YACFA,MAAM,IAAIC,IAAInC,OAAOe,MAAM,EAAE;QAC/B,EAAE,OAAOqB,GAAG;YACV,kDAAkD;YAClD,OAAO;QACT;QAEA,OAAOF,IAAIf,QAAQ;IACrB,GAAG;QAACnB,OAAOe,MAAM;QAAEf,OAAOiC,UAAU;QAAEjC,OAAOW,OAAO;QAAEX,OAAOmB,QAAQ;KAAC;IAEtE,qBACE,6BAACkB,gDAAe,CAACC,QAAQ;QAACT,OAAOA;OAC9BL;AAGP"}