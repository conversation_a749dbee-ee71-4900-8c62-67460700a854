{"version": 3, "sources": ["../../../src/server/app-render/create-server-components-renderer.tsx"], "names": ["createServerComponentRenderer", "ComponentToRender", "ComponentMod", "inlinedDataTransformStream", "clientReferenceManifest", "formState", "nonce", "serverComponentsErrorHandler", "flightStream", "createFlightStream", "props", "renderToReadableStream", "clientModules", "onError", "flightResponseRef", "current", "writable", "ServerComponentWrapper", "response", "useFlightResponse", "use"], "mappings": ";;;;+BAqBgBA;;;eAAAA;;;+DAhBW;mCACO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAe3B,SAASA,8BACdC,iBAAwC,EACxC,EACEC,YAAY,EACZC,0BAA0B,EAC1BC,uBAAuB,EACvBC,SAAS,EACTC,KAAK,EACLC,4BAA4B,EACG;IAEjC,IAAIC;IACJ,MAAMC,qBAAqB,CAACC;QAC1B,IAAI,CAACF,cAAc;YACjBA,eAAeN,aAAaS,sBAAsB,eAChD,6BAACV,mBAAuBS,QACxBN,wBAAwBQ,aAAa,EACrC;gBACEC,SAASN;YACX;QAEJ;QACA,OAAOC;IACT;IAEA,MAAMM,oBAAuC;QAAEC,SAAS;IAAK;IAE7D,MAAMC,WAAWb,2BAA2Ba,QAAQ;IACpD,OAAO,SAASC,uBAAuBP,KAAY;QACjD,MAAMQ,WAAWC,IAAAA,oCAAiB,EAChCH,UACAP,mBAAmBC,QACnBN,yBACAU,mBACAT,WACAC;QAEF,OAAOc,IAAAA,UAAG,EAACF;IACb;AACF"}