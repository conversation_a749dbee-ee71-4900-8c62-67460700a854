{"version": 3, "sources": ["../../../src/server/app-render/action-encryption.ts"], "names": ["encryptActionBoundArgs", "decryptActionBoundArgs", "textEncoder", "TextEncoder", "textDecoder", "TextDecoder", "decodeActionBoundArg", "actionId", "arg", "key", "getActionEncryptionKey", "Error", "originalPayload", "atob", "ivValue", "slice", "payload", "undefined", "decrypted", "decode", "decrypt", "stringToUint8Array", "startsWith", "length", "encodeActionBoundArg", "randomBytes", "Uint8Array", "crypto", "getRandomValues", "arrayBufferToString", "buffer", "encrypted", "encrypt", "encode", "btoa", "args", "clientReferenceManifestSingleton", "getClientReferenceManifestSingleton", "serialized", "streamToString", "renderToReadableStream", "clientModules", "decryped", "deserialized", "createFromReadableStream", "ReadableStream", "start", "controller", "enqueue", "close", "ssrManifest", "moduleLoading", "moduleMap", "serverModuleMap", "getServerModuleMap", "transformed", "decodeReply", "encodeReply"], "mappings": "AAAA,oDAAoD;;;;;;;;;;;;;;;IA8E9BA,sBAAsB;eAAtBA;;IAiBAC,sBAAsB;eAAtBA;;;QA9Ff;4BAMA;4BAKA;sCAEwB;uCASxB;AAEP,MAAMC,cAAc,IAAIC;AACxB,MAAMC,cAAc,IAAIC;AAExB,eAAeC,qBAAqBC,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMC,IAAAA,6CAAsB;IACxC,IAAI,OAAOD,QAAQ,aAAa;QAC9B,MAAM,IAAIE,MACR,CAAC,kEAAkE,CAAC;IAExE;IAEA,sDAAsD;IACtD,MAAMC,kBAAkBC,KAAKL;IAC7B,MAAMM,UAAUF,gBAAgBG,KAAK,CAAC,GAAG;IACzC,MAAMC,UAAUJ,gBAAgBG,KAAK,CAAC;IACtC,IAAIC,YAAYC,WAAW;QACzB,MAAM,IAAIN,MAAM;IAClB;IAEA,MAAMO,YAAYd,YAAYe,MAAM,CAClC,MAAMC,IAAAA,8BAAO,EAACX,KAAKY,IAAAA,yCAAkB,EAACP,UAAUO,IAAAA,yCAAkB,EAACL;IAGrE,IAAI,CAACE,UAAUI,UAAU,CAACf,WAAW;QACnC,MAAM,IAAII,MAAM;IAClB;IAEA,OAAOO,UAAUH,KAAK,CAACR,SAASgB,MAAM;AACxC;AAEA,eAAeC,qBAAqBjB,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMC,IAAAA,6CAAsB;IACxC,IAAID,QAAQQ,WAAW;QACrB,MAAM,IAAIN,MACR,CAAC,kEAAkE,CAAC;IAExE;IAEA,6BAA6B;IAC7B,MAAMc,cAAc,IAAIC,WAAW;IACnCC,OAAOC,eAAe,CAACH;IACvB,MAAMX,UAAUe,IAAAA,0CAAmB,EAACJ,YAAYK,MAAM;IAEtD,MAAMC,YAAY,MAAMC,IAAAA,8BAAO,EAC7BvB,KACAgB,aACAvB,YAAY+B,MAAM,CAAC1B,WAAWC;IAGhC,OAAO0B,KAAKpB,UAAUe,IAAAA,0CAAmB,EAACE;AAC5C;AAGO,eAAe/B,uBAAuBO,QAAgB,EAAE4B,IAAW;IACxE,MAAMC,mCAAmCC,IAAAA,0DAAmC;IAE5E,oDAAoD;IACpD,MAAMC,aAAa,MAAMC,IAAAA,oCAAc,EACrCC,IAAAA,kCAAsB,EAACL,MAAMC,iCAAiCK,aAAa;IAG7E,gEAAgE;IAChE,gFAAgF;IAChF,iBAAiB;IACjB,MAAMV,YAAY,MAAMP,qBAAqBjB,UAAU+B;IAEvD,OAAOP;AACT;AAGO,eAAe9B,uBACpBM,QAAgB,EAChBwB,SAA0B;IAE1B,gEAAgE;IAChE,MAAMW,WAAW,MAAMpC,qBAAqBC,UAAU,MAAMwB;IAE5D,wDAAwD;IACxD,MAAMY,eAAe,MAAMC,IAAAA,oCAAwB,EACjD,IAAIC,eAAe;QACjBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAAC,IAAI7C,cAAc8B,MAAM,CAACS;YAC5CK,WAAWE,KAAK;QAClB;IACF,IACA;QACEC,aAAa;YACX,0EAA0E;YAC1E,uEAAuE;YACvE,8BAA8B;YAC9B,+CAA+C;YAC/CC,eAAe,CAAC;YAChBC,WAAW,CAAC;QACd;IACF;IAGF,oEAAoE;IACpE,MAAMC,kBAAkBC,IAAAA,yCAAkB;IAC1C,MAAMC,cAAc,MAAMC,IAAAA,uBAAW,EACnC,MAAMC,IAAAA,uBAAW,EAACd,eAClBU;IAGF,OAAOE;AACT"}