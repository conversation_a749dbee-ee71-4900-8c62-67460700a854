{"version": 3, "sources": ["../../../../src/server/api-utils/node/parse-body.ts"], "names": ["parseBody", "parseJson", "str", "length", "JSON", "parse", "e", "ApiError", "req", "limit", "contentType", "headers", "type", "parameters", "encoding", "charset", "buffer", "getRawBody", "require", "isError", "body", "toString", "qs", "decode"], "mappings": ";;;;+BA4BsBA;;;eAAAA;;;6BAzBA;gEACF;uBACK;;;;;;AAEzB;;;CAGC,GACD,SAASC,UAAUC,GAAW;IAC5B,IAAIA,IAAIC,MAAM,KAAK,GAAG;QACpB,qEAAqE;QACrE,OAAO,CAAC;IACV;IAEA,IAAI;QACF,OAAOC,KAAKC,KAAK,CAACH;IACpB,EAAE,OAAOI,GAAG;QACV,MAAM,IAAIC,eAAQ,CAAC,KAAK;IAC1B;AACF;AAMO,eAAeP,UACpBQ,GAAoB,EACpBC,KAAgB;IAEhB,IAAIC;IACJ,IAAI;QACFA,cAAcL,IAAAA,kBAAK,EAACG,IAAIG,OAAO,CAAC,eAAe,IAAI;IACrD,EAAE,OAAM;QACND,cAAcL,IAAAA,kBAAK,EAAC;IACtB;IACA,MAAM,EAAEO,IAAI,EAAEC,UAAU,EAAE,GAAGH;IAC7B,MAAMI,WAAWD,WAAWE,OAAO,IAAI;IAEvC,IAAIC;IAEJ,IAAI;QACF,MAAMC,aACJC,QAAQ;QACVF,SAAS,MAAMC,WAAWT,KAAK;YAAEM;YAAUL;QAAM;IACnD,EAAE,OAAOH,GAAG;QACV,IAAIa,IAAAA,gBAAO,EAACb,MAAMA,EAAEM,IAAI,KAAK,oBAAoB;YAC/C,MAAM,IAAIL,eAAQ,CAAC,KAAK,CAAC,cAAc,EAAEE,MAAM,MAAM,CAAC;QACxD,OAAO;YACL,MAAM,IAAIF,eAAQ,CAAC,KAAK;QAC1B;IACF;IAEA,MAAMa,OAAOJ,OAAOK,QAAQ;IAE5B,IAAIT,SAAS,sBAAsBA,SAAS,uBAAuB;QACjE,OAAOX,UAAUmB;IACnB,OAAO,IAAIR,SAAS,qCAAqC;QACvD,MAAMU,KAAKJ,QAAQ;QACnB,OAAOI,GAAGC,MAAM,CAACH;IACnB,OAAO;QACL,OAAOA;IACT;AACF"}