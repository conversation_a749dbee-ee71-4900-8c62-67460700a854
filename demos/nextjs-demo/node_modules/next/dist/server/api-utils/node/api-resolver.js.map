{"version": 3, "sources": ["../../../../src/server/api-utils/node/api-resolver.ts"], "names": ["apiResolver", "getMaxContentLength", "responseLimit", "bytes", "parse", "RESPONSE_LIMIT_DEFAULT", "sendData", "req", "res", "body", "undefined", "end", "statusCode", "removeHeader", "process", "env", "NODE_ENV", "console", "warn", "url", "contentType", "<PERSON><PERSON><PERSON><PERSON>", "Stream", "<PERSON><PERSON><PERSON><PERSON>", "pipe", "isJSONLike", "includes", "stringifiedBody", "JSON", "stringify", "etag", "generateETag", "sendEtagResponse", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "length", "byteLength", "<PERSON><PERSON><PERSON>", "jsonBody", "send", "isValidData", "str", "setDraftMode", "options", "previewModeId", "Error", "expires", "enable", "Date", "serialize", "require", "previous", "Array", "isArray", "COOKIE_NAME_PRERENDER_BYPASS", "httpOnly", "sameSite", "secure", "path", "setPreviewData", "data", "previewModeEncryptionKey", "previewModeSigningKey", "jsonwebtoken", "encryptWithSecret", "payload", "sign", "from", "algorithm", "maxAge", "expiresIn", "COOKIE_NAME_PRERENDER_DATA", "revalidate", "url<PERSON><PERSON>", "opts", "context", "startsWith", "revalidateHeaders", "PRERENDER_REVALIDATE_HEADER", "unstable_onlyGenerated", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "allowedRevalidateHeaderKeys", "trustHostHeader", "key", "Object", "keys", "headers", "fetch", "host", "method", "cacheHeader", "get", "toUpperCase", "status", "err", "isError", "message", "query", "resolverModule", "apiContext", "propagateError", "dev", "page", "apiReq", "apiRes", "config", "getTracer", "<PERSON><PERSON><PERSON><PERSON>", "api", "externalResolver", "setLazyProp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tryGetPreviewData", "previewData", "preview", "parseBody", "sizeLimit", "contentLength", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writeData", "write", "endResponse", "args", "apply", "format", "sendStatusCode", "json", "redirect", "statusOrUrl", "assign", "clearPreviewData", "resolver", "interopDefault", "wasPiped", "once", "getRootSpanAttributes", "set", "apiRouteResult", "trace", "NodeSpan", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "Response", "isResSent", "ApiError", "sendError", "error"], "mappings": ";;;;+BAgUsBA;;;eAAAA;;;8DA1TJ;sBACW;6BACI;wBACV;gEACH;uBACM;gCACK;uBAWxB;iCACyB;wBACN;2BACD;4BAIlB;mCAC2B;2BACR;;;;;;AAe1B,SAASC,oBAAoBC,aAA6B;IACxD,IAAIA,iBAAiB,OAAOA,kBAAkB,WAAW;QACvD,OAAOC,cAAK,CAACC,KAAK,CAACF;IACrB;IACA,OAAOG,6BAAsB;AAC/B;AAEA;;;;;CAKC,GACD,SAASC,SAASC,GAAmB,EAAEC,GAAoB,EAAEC,IAAS;IACpE,IAAIA,SAAS,QAAQA,SAASC,WAAW;QACvCF,IAAIG,GAAG;QACP;IACF;IAEA,gCAAgC;IAChC,IAAIH,IAAII,UAAU,KAAK,OAAOJ,IAAII,UAAU,KAAK,KAAK;QACpDJ,IAAIK,YAAY,CAAC;QACjBL,IAAIK,YAAY,CAAC;QACjBL,IAAIK,YAAY,CAAC;QAEjB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiBP,MAAM;YAClDQ,QAAQC,IAAI,CACV,CAAC,yDAAyD,EAAEX,IAAIY,GAAG,CAAC,6CAA6C,CAAC,GAChH,CAAC,2EAA2E,CAAC;QAEnF;QACAX,IAAIG,GAAG;QACP;IACF;IAEA,MAAMS,cAAcZ,IAAIa,SAAS,CAAC;IAElC,IAAIZ,gBAAgBa,cAAM,EAAE;QAC1B,IAAI,CAACF,aAAa;YAChBZ,IAAIe,SAAS,CAAC,gBAAgB;QAChC;QACAd,KAAKe,IAAI,CAAChB;QACV;IACF;IAEA,MAAMiB,aAAa;QAAC;QAAU;QAAU;KAAU,CAACC,QAAQ,CAAC,OAAOjB;IACnE,MAAMkB,kBAAkBF,aAAaG,KAAKC,SAAS,CAACpB,QAAQA;IAC5D,MAAMqB,OAAOC,IAAAA,kBAAY,EAACJ;IAC1B,IAAIK,IAAAA,6BAAgB,EAACzB,KAAKC,KAAKsB,OAAO;QACpC;IACF;IAEA,IAAIG,OAAOC,QAAQ,CAACzB,OAAO;QACzB,IAAI,CAACW,aAAa;YAChBZ,IAAIe,SAAS,CAAC,gBAAgB;QAChC;QACAf,IAAIe,SAAS,CAAC,kBAAkBd,KAAK0B,MAAM;QAC3C3B,IAAIG,GAAG,CAACF;QACR;IACF;IAEA,IAAIgB,YAAY;QACdjB,IAAIe,SAAS,CAAC,gBAAgB;IAChC;IAEAf,IAAIe,SAAS,CAAC,kBAAkBU,OAAOG,UAAU,CAACT;IAClDnB,IAAIG,GAAG,CAACgB;AACV;AAEA;;;;CAIC,GACD,SAASU,SAAS7B,GAAoB,EAAE8B,QAAa;IACnD,iCAAiC;IACjC9B,IAAIe,SAAS,CAAC,gBAAgB;IAE9B,6BAA6B;IAC7Bf,IAAI+B,IAAI,CAACX,KAAKC,SAAS,CAACS;AAC1B;AAEA,SAASE,YAAYC,GAAQ;IAC3B,OAAO,OAAOA,QAAQ,YAAYA,IAAIN,MAAM,IAAI;AAClD;AAEA,SAASO,aACPlC,GAAuB,EACvBmC,OAGC;IAED,IAAI,CAACH,YAAYG,QAAQC,aAAa,GAAG;QACvC,MAAM,IAAIC,MAAM;IAClB;IACA,MAAMC,UAAUH,QAAQI,MAAM,GAAGrC,YAAY,IAAIsC,KAAK;IACtD,2DAA2D;IAC3D,oDAAoD;IACpD,wEAAwE;IACxE,MAAM,EAAEC,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAW3C,IAAIa,SAAS,CAAC;IAC/Bb,IAAIe,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAO4B,aAAa,WACpB;YAACA;SAAS,GACVC,MAAMC,OAAO,CAACF,YACdA,WACA,EAAE;QACNF,UAAUK,mCAA4B,EAAEX,QAAQC,aAAa,EAAE;YAC7DW,UAAU;YACVC,UAAU1C,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DyC,QAAQ3C,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjC0C,MAAM;YACNZ;QACF;KACD;IACD,OAAOtC;AACT;AAEA,SAASmD,eACPnD,GAAuB,EACvBoD,IAAqB,EACrBjB,OAGqB;IAErB,IAAI,CAACH,YAAYG,QAAQC,aAAa,GAAG;QACvC,MAAM,IAAIC,MAAM;IAClB;IACA,IAAI,CAACL,YAAYG,QAAQkB,wBAAwB,GAAG;QAClD,MAAM,IAAIhB,MAAM;IAClB;IACA,IAAI,CAACL,YAAYG,QAAQmB,qBAAqB,GAAG;QAC/C,MAAM,IAAIjB,MAAM;IAClB;IAEA,MAAMkB,eACJb,QAAQ;IACV,MAAM,EAAEc,iBAAiB,EAAE,GACzBd,QAAQ;IACV,MAAMe,UAAUF,aAAaG,IAAI,CAC/B;QACEN,MAAMI,kBACJ/B,OAAOkC,IAAI,CAACxB,QAAQkB,wBAAwB,GAC5CjC,KAAKC,SAAS,CAAC+B;IAEnB,GACAjB,QAAQmB,qBAAqB,EAC7B;QACEM,WAAW;QACX,GAAIzB,QAAQ0B,MAAM,KAAK3D,YACnB;YAAE4D,WAAW3B,QAAQ0B,MAAM;QAAC,IAC5B3D,SAAS;IACf;IAGF,qEAAqE;IACrE,+CAA+C;IAC/C,IAAIuD,QAAQ9B,MAAM,GAAG,MAAM;QACzB,MAAM,IAAIU,MACR,CAAC,0GAA0G,CAAC;IAEhH;IAEA,MAAM,EAAEI,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAW3C,IAAIa,SAAS,CAAC;IAC/Bb,IAAIe,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAO4B,aAAa,WACpB;YAACA;SAAS,GACVC,MAAMC,OAAO,CAACF,YACdA,WACA,EAAE;QACNF,UAAUK,mCAA4B,EAAEX,QAAQC,aAAa,EAAE;YAC7DW,UAAU;YACVC,UAAU1C,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DyC,QAAQ3C,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjC0C,MAAM;YACN,GAAIf,QAAQ0B,MAAM,KAAK3D,YAClB;gBAAE2D,QAAQ1B,QAAQ0B,MAAM;YAAC,IAC1B3D,SAAS;YACb,GAAIiC,QAAQe,IAAI,KAAKhD,YAChB;gBAAEgD,MAAMf,QAAQe,IAAI;YAAC,IACtBhD,SAAS;QACf;QACAuC,UAAUsB,iCAA0B,EAAEN,SAAS;YAC7CV,UAAU;YACVC,UAAU1C,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DyC,QAAQ3C,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjC0C,MAAM;YACN,GAAIf,QAAQ0B,MAAM,KAAK3D,YAClB;gBAAE2D,QAAQ1B,QAAQ0B,MAAM;YAAC,IAC1B3D,SAAS;YACb,GAAIiC,QAAQe,IAAI,KAAKhD,YAChB;gBAAEgD,MAAMf,QAAQe,IAAI;YAAC,IACtBhD,SAAS;QACf;KACD;IACD,OAAOF;AACT;AAEA,eAAegE,WACbC,OAAe,EACfC,IAEC,EACDnE,GAAoB,EACpBoE,OAAmB;IAEnB,IAAI,OAAOF,YAAY,YAAY,CAACA,QAAQG,UAAU,CAAC,MAAM;QAC3D,MAAM,IAAI/B,MACR,CAAC,qFAAqF,EAAE4B,QAAQ,CAAC;IAErG;IACA,MAAMI,oBAAiC;QACrC,CAACC,uCAA2B,CAAC,EAAEH,QAAQ/B,aAAa;QACpD,GAAI8B,KAAKK,sBAAsB,GAC3B;YACE,CAACC,sDAA0C,CAAC,EAAE;QAChD,IACA,CAAC,CAAC;IACR;IACA,MAAMC,8BAA8B;WAC9BN,QAAQM,2BAA2B,IAAI,EAAE;WACzCN,QAAQO,eAAe,GACvB;YAAC;YAAU;SAA6B,GACxC,EAAE;KACP;IAED,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAAC9E,IAAI+E,OAAO,EAAG;QAC1C,IAAIL,4BAA4BvD,QAAQ,CAACyD,MAAM;YAC7CN,iBAAiB,CAACM,IAAI,GAAG5E,IAAI+E,OAAO,CAACH,IAAI;QAC3C;IACF;IAEA,IAAI;QACF,IAAIR,QAAQO,eAAe,EAAE;YAC3B,MAAM1E,MAAM,MAAM+E,MAAM,CAAC,QAAQ,EAAEhF,IAAI+E,OAAO,CAACE,IAAI,CAAC,EAAEf,QAAQ,CAAC,EAAE;gBAC/DgB,QAAQ;gBACRH,SAAST;YACX;YACA,gEAAgE;YAChE,qEAAqE;YACrE,gEAAgE;YAChE,MAAMa,cACJlF,IAAI8E,OAAO,CAACK,GAAG,CAAC,qBAAqBnF,IAAI8E,OAAO,CAACK,GAAG,CAAC;YAEvD,IACED,CAAAA,+BAAAA,YAAaE,WAAW,QAAO,iBAC/B,CAAEpF,CAAAA,IAAIqF,MAAM,KAAK,OAAOnB,KAAKK,sBAAsB,AAAD,GAClD;gBACA,MAAM,IAAIlC,MAAM,CAAC,iBAAiB,EAAErC,IAAIqF,MAAM,CAAC,CAAC;YAClD;QACF,OAAO,IAAIlB,QAAQH,UAAU,EAAE;YAC7B,MAAMG,QAAQH,UAAU,CAAC;gBACvBC;gBACAI;gBACAH;YACF;QACF,OAAO;YACL,MAAM,IAAI7B,MACR,CAAC,sEAAsE,CAAC;QAE5E;IACF,EAAE,OAAOiD,KAAc;QACrB,MAAM,IAAIjD,MACR,CAAC,qBAAqB,EAAE4B,QAAQ,EAAE,EAAEsB,IAAAA,gBAAO,EAACD,OAAOA,IAAIE,OAAO,GAAGF,IAAI,CAAC;IAE1E;AACF;AAEO,eAAe9F,YACpBO,GAAoB,EACpBC,GAAmB,EACnByF,KAAU,EACVC,cAAmB,EACnBC,UAAsB,EACtBC,cAAuB,EACvBC,GAAa,EACbC,IAAa;IAEb,MAAMC,SAAShG;IACf,MAAMiG,SAAShG;IAEf,IAAI;YAOiBiG,aACGA,cACGA,cA4EzBC;QApFA,IAAI,CAACR,gBAAgB;YACnB1F,IAAII,UAAU,GAAG;YACjBJ,IAAIG,GAAG,CAAC;YACR;QACF;QACA,MAAM8F,SAAqBP,eAAeO,MAAM,IAAI,CAAC;QACrD,MAAME,aAAaF,EAAAA,cAAAA,OAAOG,GAAG,qBAAVH,YAAYE,UAAU,MAAK;QAC9C,MAAMzG,gBAAgBuG,EAAAA,eAAAA,OAAOG,GAAG,qBAAVH,aAAYvG,aAAa,KAAI;QACnD,MAAM2G,mBAAmBJ,EAAAA,eAAAA,OAAOG,GAAG,qBAAVH,aAAYI,gBAAgB,KAAI;QAEzD,qBAAqB;QACrBC,IAAAA,kBAAW,EAAC;YAAEvG,KAAKgG;QAAO,GAAG,WAAWQ,IAAAA,gCAAe,EAACxG,IAAI+E,OAAO;QACnE,uBAAuB;QACvBiB,OAAON,KAAK,GAAGA;QACf,uBAAuB;QACvBa,IAAAA,kBAAW,EAAC;YAAEvG,KAAKgG;QAAO,GAAG,eAAe,IAC1CS,IAAAA,oCAAiB,EAACzG,KAAKC,KAAK2F;QAE9B,sCAAsC;QACtCW,IAAAA,kBAAW,EAAC;YAAEvG,KAAKgG;QAAO,GAAG,WAAW,IACtCA,OAAOU,WAAW,KAAK,QAAQ,OAAOvG;QAExC,6CAA6C;QAC7CoG,IAAAA,kBAAW,EAAC;YAAEvG,KAAKgG;QAAO,GAAG,aAAa,IAAMA,OAAOW,OAAO;QAE9D,kBAAkB;QAClB,IAAIP,cAAc,CAACJ,OAAO9F,IAAI,EAAE;YAC9B8F,OAAO9F,IAAI,GAAG,MAAM0G,IAAAA,oBAAS,EAC3BZ,QACAE,OAAOG,GAAG,IAAIH,OAAOG,GAAG,CAACD,UAAU,IAAIF,OAAOG,GAAG,CAACD,UAAU,CAACS,SAAS,GAClEX,OAAOG,GAAG,CAACD,UAAU,CAACS,SAAS,GAC/B;QAER;QAEA,IAAIC,gBAAgB;QACpB,MAAMC,mBAAmBrH,oBAAoBC;QAC7C,MAAMqH,YAAYf,OAAOgB,KAAK;QAC9B,MAAMC,cAAcjB,OAAO7F,GAAG;QAC9B6F,OAAOgB,KAAK,GAAG,CAAC,GAAGE;YACjBL,iBAAiBpF,OAAOG,UAAU,CAACsF,IAAI,CAAC,EAAE,IAAI;YAC9C,OAAOH,UAAUI,KAAK,CAACnB,QAAQkB;QACjC;QACAlB,OAAO7F,GAAG,GAAG,CAAC,GAAG+G;YACf,IAAIA,KAAKvF,MAAM,IAAI,OAAOuF,IAAI,CAAC,EAAE,KAAK,YAAY;gBAChDL,iBAAiBpF,OAAOG,UAAU,CAACsF,IAAI,CAAC,EAAE,IAAI;YAChD;YAEA,IAAIxH,iBAAiBmH,iBAAiBC,kBAAkB;gBACtDrG,QAAQC,IAAI,CACV,CAAC,iBAAiB,EAAEX,IAAIY,GAAG,CAAC,SAAS,EAAEhB,cAAK,CAACyH,MAAM,CACjDN,kBACA,0GAA0G,CAAC;YAEjH;YAEA,OAAOG,YAAYE,KAAK,CAACnB,QAAQkB;QACnC;QACAlB,OAAOX,MAAM,GAAG,CAACjF,aAAeiH,IAAAA,qBAAc,EAACrB,QAAQ5F;QACvD4F,OAAOjE,IAAI,GAAG,CAACqB,OAAStD,SAASiG,QAAQC,QAAQ5C;QACjD4C,OAAOsB,IAAI,GAAG,CAAClE,OAASvB,SAASmE,QAAQ5C;QACzC4C,OAAOuB,QAAQ,GAAG,CAACC,aAA8B7G,MAC/C4G,IAAAA,eAAQ,EAACvB,QAAQwB,aAAa7G;QAChCqF,OAAO9D,YAAY,GAAG,CAACC,UAAU;YAAEI,QAAQ;QAAK,CAAC,GAC/CL,aAAa8D,QAAQpB,OAAO6C,MAAM,CAAC,CAAC,GAAG9B,YAAYxD;QACrD6D,OAAO7C,cAAc,GAAG,CAACC,MAAMjB,UAAU,CAAC,CAAC,GACzCgB,eAAe6C,QAAQ5C,MAAMwB,OAAO6C,MAAM,CAAC,CAAC,GAAG9B,YAAYxD;QAC7D6D,OAAO0B,gBAAgB,GAAG,CAACvF,UAAU,CAAC,CAAC,GACrCuF,IAAAA,uBAAgB,EAAC1B,QAAQ7D;QAC3B6D,OAAOhC,UAAU,GAAG,CAClBC,SACAC,OAGGF,WAAWC,SAASC,QAAQ,CAAC,GAAGnE,KAAK4F;QAE1C,MAAMgC,WAAWC,IAAAA,8BAAc,EAAClC;QAChC,IAAImC,WAAW;QAEf,IAAIvH,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,uDAAuD;YACvDR,IAAI8H,IAAI,CAAC,QAAQ,IAAOD,WAAW;QACrC;SAEA3B,mCAAAA,IAAAA,iBAAS,IAAG6B,qBAAqB,uBAAjC7B,iCAAqC8B,GAAG,CAAC,cAAclC;QACvD,wBAAwB;QACxB,MAAMmC,iBAAiB,MAAM/B,IAAAA,iBAAS,IAAGgC,KAAK,CAC5CC,mBAAQ,CAACC,UAAU,EACnB;YACEC,UAAU,CAAC,4BAA4B,EAAEvC,KAAK,CAAC;QACjD,GACA,IAAM6B,SAAS5H,KAAKC;QAGtB,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,IAAI,OAAOyH,mBAAmB,aAAa;gBACzC,IAAIA,0BAA0BK,UAAU;oBACtC,MAAM,IAAIjG,MACR;gBAEJ;gBACA5B,QAAQC,IAAI,CACV,CAAC,gDAAgD,EAAE,OAAOuH,eAAe,CAAC,CAAC;YAE/E;YAEA,IAAI,CAAC5B,oBAAoB,CAACkC,IAAAA,gBAAS,EAACvI,QAAQ,CAAC6H,UAAU;gBACrDpH,QAAQC,IAAI,CACV,CAAC,4CAA4C,EAAEX,IAAIY,GAAG,CAAC,sCAAsC,CAAC;YAElG;QACF;IACF,EAAE,OAAO2E,KAAK;QACZ,IAAIA,eAAekD,eAAQ,EAAE;YAC3BC,IAAAA,gBAAS,EAACzC,QAAQV,IAAIlF,UAAU,EAAEkF,IAAIE,OAAO;QAC/C,OAAO;YACL,IAAIK,KAAK;gBACP,IAAIN,IAAAA,gBAAO,EAACD,MAAM;oBAChBA,IAAIQ,IAAI,GAAGA;gBACb;gBACA,MAAMR;YACR;YAEA7E,QAAQiI,KAAK,CAACpD;YACd,IAAIM,gBAAgB;gBAClB,MAAMN;YACR;YACAmD,IAAAA,gBAAS,EAACzC,QAAQ,KAAK;QACzB;IACF;AACF"}