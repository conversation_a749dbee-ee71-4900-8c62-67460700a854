{"version": 3, "sources": ["../../../src/server/base-http/node.ts"], "names": ["NodeNextRequest", "NodeNextResponse", "NEXT_REQUEST_META", "BaseNextRequest", "originalRequest", "_req", "url", "cookies", "value", "constructor", "method", "toUpperCase", "headers", "BaseNextResponse", "originalResponse", "SYMBOL_CLEARED_COOKIES", "_res", "textBody", "undefined", "sent", "finished", "headersSent", "statusCode", "statusMessage", "<PERSON><PERSON><PERSON><PERSON>", "name", "removeHeader", "getHeader<PERSON><PERSON>ues", "values", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "map", "toString", "<PERSON><PERSON><PERSON><PERSON>", "join", "getHeaders", "append<PERSON><PERSON>er", "currentV<PERSON>ues", "includes", "body", "send", "end"], "mappings": ";;;;;;;;;;;;;;;IAiBaA,eAAe;eAAfA;;IAuBAC,gBAAgB;eAAhBA;;;0BArC0B;6BAGL;uBAGgB;IAW/CC,qBAAAA,8BAAiB;AAHb,MAAMF,wBAAwBG,sBAAe;IAKlD,IAAIC,kBAAkB;QACpB,qFAAqF;QACrF,+BAA+B;QAC/B,IAAI,CAACC,IAAI,CAACH,8BAAiB,CAAC,GAAG,IAAI,CAACA,8BAAiB,CAAC;QACtD,IAAI,CAACG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACA,GAAG;QACxB,IAAI,CAACD,IAAI,CAACE,OAAO,GAAG,IAAI,CAACA,OAAO;QAChC,OAAO,IAAI,CAACF,IAAI;IAClB;IAEA,IAAID,gBAAgBI,KAAU,EAAE;QAC9B,IAAI,CAACH,IAAI,GAAGG;IACd;IAEAC,YAAoBJ,KAAW;QAC7B,KAAK,CAACA,KAAKK,MAAM,CAAEC,WAAW,IAAIN,KAAKC,GAAG,EAAGD;oBAD3BA;aAjBbO,UAAU,IAAI,CAACP,IAAI,CAACO,OAAO;YAElC,CAACV,mBAAkB,GAAgB,IAAI,CAACG,IAAI,CAACH,8BAAiB,CAAC,IAAI,CAAC;IAiBpE;AACF;AAEO,MAAMD,yBAAyBY,uBAAgB;IAKpD,IAAIC,mBAAmB;QACrB,IAAIC,gCAAsB,IAAI,IAAI,EAAE;YAClC,IAAI,CAACC,IAAI,CAACD,gCAAsB,CAAC,GAAG,IAAI,CAACA,gCAAsB,CAAC;QAClE;QAEA,OAAO,IAAI,CAACC,IAAI;IAClB;IAEAP,YACUO,KACR;QACA,KAAK,CAACA;oBAFEA;aAbFC,WAA+BC;IAgBvC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACH,IAAI,CAACI,QAAQ,IAAI,IAAI,CAACJ,IAAI,CAACK,WAAW;IACpD;IAEA,IAAIC,aAAa;QACf,OAAO,IAAI,CAACN,IAAI,CAACM,UAAU;IAC7B;IAEA,IAAIA,WAAWd,KAAa,EAAE;QAC5B,IAAI,CAACQ,IAAI,CAACM,UAAU,GAAGd;IACzB;IAEA,IAAIe,gBAAgB;QAClB,OAAO,IAAI,CAACP,IAAI,CAACO,aAAa;IAChC;IAEA,IAAIA,cAAcf,KAAa,EAAE;QAC/B,IAAI,CAACQ,IAAI,CAACO,aAAa,GAAGf;IAC5B;IAEAgB,UAAUC,IAAY,EAAEjB,KAAwB,EAAQ;QACtD,IAAI,CAACQ,IAAI,CAACQ,SAAS,CAACC,MAAMjB;QAC1B,OAAO,IAAI;IACb;IAEAkB,aAAaD,IAAY,EAAQ;QAC/B,IAAI,CAACT,IAAI,CAACU,YAAY,CAACD;QACvB,OAAO,IAAI;IACb;IAEAE,gBAAgBF,IAAY,EAAwB;QAClD,MAAMG,SAAS,IAAI,CAACZ,IAAI,CAACa,SAAS,CAACJ;QAEnC,IAAIG,WAAWV,WAAW,OAAOA;QAEjC,OAAO,AAACY,CAAAA,MAAMC,OAAO,CAACH,UAAUA,SAAS;YAACA;SAAO,AAAD,EAAGI,GAAG,CAAC,CAACxB,QACtDA,MAAMyB,QAAQ;IAElB;IAEAC,UAAUT,IAAY,EAAW;QAC/B,OAAO,IAAI,CAACT,IAAI,CAACkB,SAAS,CAACT;IAC7B;IAEAI,UAAUJ,IAAY,EAAsB;QAC1C,MAAMG,SAAS,IAAI,CAACD,eAAe,CAACF;QACpC,OAAOK,MAAMC,OAAO,CAACH,UAAUA,OAAOO,IAAI,CAAC,OAAOjB;IACpD;IAEAkB,aAAkC;QAChC,OAAO,IAAI,CAACpB,IAAI,CAACoB,UAAU;IAC7B;IAEAC,aAAaZ,IAAY,EAAEjB,KAAa,EAAQ;QAC9C,MAAM8B,gBAAgB,IAAI,CAACX,eAAe,CAACF,SAAS,EAAE;QAEtD,IAAI,CAACa,cAAcC,QAAQ,CAAC/B,QAAQ;YAClC,IAAI,CAACQ,IAAI,CAACQ,SAAS,CAACC,MAAM;mBAAIa;gBAAe9B;aAAM;QACrD;QAEA,OAAO,IAAI;IACb;IAEAgC,KAAKhC,KAAa,EAAE;QAClB,IAAI,CAACS,QAAQ,GAAGT;QAChB,OAAO,IAAI;IACb;IAEAiC,OAAO;QACL,IAAI,CAACzB,IAAI,CAAC0B,GAAG,CAAC,IAAI,CAACzB,QAAQ;IAC7B;AACF"}