{"version": 3, "sources": ["../../src/server/render-result.ts"], "names": ["RenderResult", "fromStatic", "value", "metadata", "constructor", "response", "contentType", "waitUntil", "assignMetadata", "Object", "assign", "isNull", "isDynamic", "toUnchunkedString", "stream", "Error", "streamToString", "readable", "Array", "isArray", "chainStreams", "chain", "responses", "streamFromString", "push", "pipeTo", "writable", "preventClose", "close", "err", "isAbortError", "abort", "pipeToNodeResponse", "res"], "mappings": ";;;;+BA8DA;;;eAAqBA;;;sCAtDd;8BAC0C;AAqDlC,MAAMA;IAuBnB;;;;;GAKC,GACD,OAAcC,WAAWC,KAAa,EAAE;QACtC,OAAO,IAAIF,aAAyCE,OAAO;YAAEC,UAAU,CAAC;QAAE;IAC5E;IAIAC,YACEC,QAA8B,EAC9B,EAAEC,WAAW,EAAEC,SAAS,EAAEJ,QAAQ,EAAiC,CACnE;QACA,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACC,WAAW,GAAGA;QACnB,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACI,SAAS,GAAGA;IACnB;IAEOC,eAAeL,QAAkB,EAAE;QACxCM,OAAOC,MAAM,CAAC,IAAI,CAACP,QAAQ,EAAEA;IAC/B;IAEA;;;GAGC,GACD,IAAWQ,SAAkB;QAC3B,OAAO,IAAI,CAACN,QAAQ,KAAK;IAC3B;IAEA;;;GAGC,GACD,IAAWO,YAAqB;QAC9B,OAAO,OAAO,IAAI,CAACP,QAAQ,KAAK;IAClC;IAWOQ,kBAAkBC,SAAS,KAAK,EAA4B;QACjE,IAAI,IAAI,CAACT,QAAQ,KAAK,MAAM;YAC1B,MAAM,IAAIU,MAAM;QAClB;QAEA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,IAAI,CAACS,QAAQ;gBACX,MAAM,IAAIC,MACR;YAEJ;YAEA,OAAOC,IAAAA,oCAAc,EAAC,IAAI,CAACC,QAAQ;QACrC;QAEA,OAAO,IAAI,CAACZ,QAAQ;IACtB;IAEA;;;GAGC,GACD,IAAYY,WAAuC;QACjD,IAAI,IAAI,CAACZ,QAAQ,KAAK,MAAM;YAC1B,MAAM,IAAIU,MAAM;QAClB;QACA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,MAAM,IAAIU,MAAM;QAClB;QAEA,oEAAoE;QACpE,IAAIG,MAAMC,OAAO,CAAC,IAAI,CAACd,QAAQ,GAAG;YAChC,OAAOe,IAAAA,kCAAY,KAAI,IAAI,CAACf,QAAQ;QACtC;QAEA,OAAO,IAAI,CAACA,QAAQ;IACtB;IAEA;;;;;;;GAOC,GACD,AAAOgB,MAAMJ,QAAoC,EAAE;QACjD,IAAI,IAAI,CAACZ,QAAQ,KAAK,MAAM;YAC1B,MAAM,IAAIU,MAAM;QAClB;QAEA,mEAAmE;QACnE,IAAIO;QACJ,IAAI,OAAO,IAAI,CAACjB,QAAQ,KAAK,UAAU;YACrCiB,YAAY;gBAACC,IAAAA,sCAAgB,EAAC,IAAI,CAAClB,QAAQ;aAAE;QAC/C,OAAO,IAAIa,MAAMC,OAAO,CAAC,IAAI,CAACd,QAAQ,GAAG;YACvCiB,YAAY,IAAI,CAACjB,QAAQ;QAC3B,OAAO;YACLiB,YAAY;gBAAC,IAAI,CAACjB,QAAQ;aAAC;QAC7B;QAEA,mCAAmC;QACnCiB,UAAUE,IAAI,CAACP;QAEf,uBAAuB;QACvB,IAAI,CAACZ,QAAQ,GAAGiB;IAClB;IAEA;;;;;;GAMC,GACD,MAAaG,OAAOC,QAAoC,EAAiB;QACvE,IAAI;YACF,MAAM,IAAI,CAACT,QAAQ,CAACQ,MAAM,CAACC,UAAU;gBACnC,qEAAqE;gBACrE,sEAAsE;gBACtE,sEAAsE;gBACtE,SAAS;gBACTC,cAAc;YAChB;YAEA,iEAAiE;YACjE,+BAA+B;YAC/B,IAAI,IAAI,CAACpB,SAAS,EAAE,MAAM,IAAI,CAACA,SAAS;YAExC,6BAA6B;YAC7B,MAAMmB,SAASE,KAAK;QACtB,EAAE,OAAOC,KAAK;YACZ,wEAAwE;YACxE,0EAA0E;YAC1E,gCAAgC;YAChC,IAAIC,IAAAA,0BAAY,EAACD,MAAM;gBACrB,wDAAwD;gBACxD,MAAMH,SAASK,KAAK,CAACF;gBAErB;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,MAAMA;QACR;IACF;IAEA;;;;;GAKC,GACD,MAAaG,mBAAmBC,GAAmB,EAAE;QACnD,MAAMD,IAAAA,gCAAkB,EAAC,IAAI,CAACf,QAAQ,EAAEgB,KAAK,IAAI,CAAC1B,SAAS;IAC7D;AACF"}