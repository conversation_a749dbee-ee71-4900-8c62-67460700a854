{"version": 3, "sources": ["../../src/server/require-hook.ts"], "names": ["hookPropertyMap", "defaultOverrides", "addHookAliases", "path", "require", "mod", "originalRequire", "prototype", "resolveFilename", "_resolveFilename", "resolve", "process", "env", "NEXT_MINIMAL", "__non_webpack_require__", "Map", "dirname", "toResolveMap", "map", "Object", "entries", "key", "value", "aliases", "set", "originalResolveFilename", "requestMap", "request", "parent", "is<PERSON><PERSON>", "options", "hookResolved", "get", "call", "bind", "endsWith", "basename"], "mappings": "AAAA,uHAAuH;AACvH,0FAA0F;AAC1F,kGAAkG;AAElG,oDAAoD;;;;;;;;;;;;;;;;;IAWvCA,eAAe;eAAfA;;IAEAC,gBAAgB;eAAhBA;;IASGC,cAAc;eAAdA;;;AArBhB,MAAMC,OAAOC,QAAQ;AACrB,MAAMC,MAAMD,QAAQ;AACpB,MAAME,kBAAkBD,IAAIE,SAAS,CAACH,OAAO;AAC7C,MAAMI,kBAAkBH,IAAII,gBAAgB;AAE5C,IAAIC,UAAkCC,QAAQC,GAAG,CAACC,YAAY,GAE1DC,wBAAwBJ,OAAO,GAC/BN,QAAQM,OAAO;AAEZ,MAAMV,kBAAkB,IAAIe;AAE5B,MAAMd,mBAAmB;IAC9B,cAAcE,KAAKa,OAAO,CAACN,QAAQ;IACnC,oBAAoBA,QAAQ;IAC5B,uBAAuBA,QAAQ;AACjC;AAEA,MAAMO,eAAe,CAACC,MACpBC,OAAOC,OAAO,CAACF,KAAKA,GAAG,CAAC,CAAC,CAACG,KAAKC,MAAM,GAAK;YAACD;YAAKX,QAAQY;SAAO;AAE1D,SAASpB,eAAeqB,UAA8B,EAAE;IAC7D,KAAK,MAAM,CAACF,KAAKC,MAAM,IAAIC,QAAS;QAClCvB,gBAAgBwB,GAAG,CAACH,KAAKC;IAC3B;AACF;AAEApB,eAAee,aAAahB;AAE5BI,IAAII,gBAAgB,GAAG,CAAA,SACrBgB,uBAKW,EACXC,UAA+B,EAC/BC,OAAe,EACfC,MAAc,EACdC,MAAe,EACfC,OAAY;IAEZ,MAAMC,eAAeL,WAAWM,GAAG,CAACL;IACpC,IAAII,cAAcJ,UAAUI;IAE5B,OAAON,wBAAwBQ,IAAI,CAAC5B,KAAKsB,SAASC,QAAQC,QAAQC;AAElE,8FAA8F;AAChG,CAAA,EAAEI,IAAI,CAAC,MAAM1B,iBAAiBR;AAE9B,2FAA2F;AAC3F,0FAA0F;AAC1F,iGAAiG;AACjGK,IAAIE,SAAS,CAACH,OAAO,GAAG,SAAUuB,OAAe;IAC/C,IAAIA,QAAQQ,QAAQ,CAAC,oBAAoB;QACvC,OAAO7B,gBAAgB2B,IAAI,CACzB,IAAI,EACJ,CAAC,8DAA8D,EAAE9B,KAAKiC,QAAQ,CAC5ET,SACA,mBACA,CAAC;IAEP;IAEA,OAAOrB,gBAAgB2B,IAAI,CAAC,IAAI,EAAEN;AACpC"}